import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login as apiLogin, logout as apiLogout, register as apiRegister, getUserInfo, updateUserInfo } from '../api/user'

export const useUserStore = defineStore('user', () => {
  // 用户状态
  const token = ref(localStorage.getItem('token') || '')
  const userId = ref(localStorage.getItem('userId') || '')
  // const username = ref(localStorage.getItem('username') || '')
  const userName = ref(localStorage.getItem('userName') || '')
  const avatar = ref(localStorage.getItem('avatar') || '')
  const role = ref(localStorage.getItem('role') || '')
  const isLoggedIn = ref(!!localStorage.getItem('token'))
  const loading = ref(false)

  // 登录
  const login = async (loginUsername, password) => {
    try {
      loading.value = true
      const result = await apiLogin(loginUsername, password)

      // 根据接口文档，登录成功返回token或用户信息
      if (result) {
        console.log('登录API返回结果:', result)

        // 假设返回的是token字符串
        let userToken = result
        let userInfo

        if (typeof result === 'string') {
          // 如果返回的是用户ID字符串，保存ID并设置临时token
          console.log('登录返回用户ID字符串:', result)
          userId.value = result // 直接使用返回的ID字符串，避免精度丢失
          userToken = 'temp_token_' + Date.now()
          token.value = userToken

          // 设置axios默认header
          setAuthToken(userToken)

          // 使用返回的用户ID获取用户详细信息
          try {
            console.log('尝试获取用户详细信息，userId:', result)
            const detailedUserInfo = await getUserInfo(result)
            console.log('获取到的详细用户信息:', detailedUserInfo)
            userInfo = detailedUserInfo
          } catch (error) {
            console.error('获取用户详细信息失败:', error)
            // 如果获取失败，使用基本信息
            userInfo = {
              id: result,
              userName: loginUsername,
              avatar: '',
              role: 'user'
            }
          }
        } else {
          // 如果直接返回用户信息
          console.log('登录返回用户信息对象:', result)
          userInfo = result.data || result // 处理可能的嵌套结构
          console.log('提取的用户信息:', userInfo)

          // 生成临时token（实际应该从响应中获取）
          userToken = userInfo.token || 'temp_token_' + Date.now()
          token.value = userToken
          setAuthToken(userToken)
        }

        // 更新状态 - 确保ID作为字符串处理，避免精度丢失
        userId.value = String(userInfo.id)
        // username.value = userInfo.userName
        userName.value = userInfo.userName
        console.log("用户名222"+ userName.value)
        avatar.value = userInfo.avatar || ''
        role.value = userInfo.role || 'user'
        isLoggedIn.value = true

        // 保存到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('userId', userId.value)
        // localStorage.setItem('username', userInfo.userName)
        localStorage.setItem('userName', userInfo.userName)
        localStorage.setItem('avatar', avatar.value)
        localStorage.setItem('role', role.value)

        return userInfo
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 设置认证token
  const setAuthToken = (authToken) => {
    // 这个函数将在API文件中实现
    if (authToken) {
      localStorage.setItem('token', authToken)
    } else {
      localStorage.removeItem('token')
    }
  }

  // 注册
  const register = async (userData) => {
    try {
      loading.value = true
      const result = await apiRegister(userData)
      return result
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      // 调用后端登出接口
      await apiLogout()
    } catch (error) {
      console.error('登出接口调用失败:', error)
      // 即使接口失败也要清除本地状态
    } finally {
      // 清除本地状态
      token.value = ''
      userId.value = ''
      userName.value = ''
      avatar.value = ''
      role.value = ''
      isLoggedIn.value = false

      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('userName')
      localStorage.removeItem('avatar')
      localStorage.removeItem('role')

      // 清除axios认证头
      setAuthToken(null)
    }
  }

  // 更新个人信息
  const updateProfile = async (profileData) => {
    try {
      loading.value = true
      const userInfo = {
        id: userId.value, // 保持字符串格式，避免精度丢失
        userName: profileData.userName || userName.value,
        avatar: profileData.avatar || avatar.value,
        role: profileData.role || role.value
      }

      await updateUserInfo(userInfo)

      // 更新本地状态
      userName.value = userInfo.userName
      avatar.value = userInfo.avatar
      role.value = userInfo.role

      // 更新本地存储
      localStorage.setItem('userName', userName.value)
      localStorage.setItem('avatar', avatar.value)
      localStorage.setItem('role', role.value)

      return userInfo
    } catch (error) {
      console.error('更新个人信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户信息
  const getCurrentUserInfo = async () => {
    try {
      if (!userId.value) {
        throw new Error('用户未登录')
      }
      loading.value = true
      const userInfo = await getUserInfo(userId.value) // 直接使用字符串ID，避免精度丢失
      console.log('获取到的用户信息:', userInfo)

      // 更新本地状态
      userName.value = userInfo.userName
      console.log('设置userName为:', userName.value)
      avatar.value = userInfo.avatar || ''
      role.value = userInfo.role || 'user'

      // 更新本地存储
      localStorage.setItem('userName', userName.value)
      localStorage.setItem('avatar', avatar.value)
      localStorage.setItem('role', role.value)

      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 初始化用户状态
  const initUserState = async () => {
    if (userId.value) {
      try {
        await getCurrentUserInfo()
      } catch (error) {
        console.error('初始化用户状态失败:', error)
        // 如果获取用户信息失败，清除登录状态
        await logout()
      }
    }
  }

  return {
    // 状态
    token,
    userId,
    userName,
    avatar,
    role,
    isLoggedIn,
    loading,

    // 方法
    login,
    register,
    logout,
    updateProfile,
    getCurrentUserInfo,
    initUserState,
    setAuthToken
  }
}) 
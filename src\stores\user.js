import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '')
  const username = ref('')
  const phone = ref('')
  const avatar = ref('')
  const isLoggedIn = ref(false)

  // 登录
  const login = async (credentials) => {
    try {
      // TODO: 替换为实际的API接口
      const response = await axios.post('/api/auth/login', credentials)
      const { token: newToken, user } = response.data

      token.value = newToken
      username.value = user.username
      phone.value = user.phone
      avatar.value = user.avatar
      isLoggedIn.value = true

      localStorage.setItem('token', newToken)
      
      // 设置axios默认header
      axios.defaults.headers.common['Authorization'] = 'Bearer ' + newToken

      return response.data
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 注册
  const register = async (userData) => {
    try {
      // TODO: 替换为实际的API接口
      const response = await axios.post('/api/auth/register', userData)
      return response.data
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }

  // 退出登录
  const logout = () => {
    token.value = ''
    username.value = ''
    phone.value = ''
    avatar.value = ''
    isLoggedIn.value = false
    localStorage.removeItem('token')
    delete axios.defaults.headers.common['Authorization']
  }

  // 更新个人信息
  const updateProfile = async (profileData) => {
    try {
      // TODO: 替换为实际的API接口
      const response = await axios.put('/api/user/profile', profileData)
      const { user } = response.data

      username.value = user.username
      phone.value = user.phone
      if (user.avatar) {
        avatar.value = user.avatar
      }

      return response.data
    } catch (error) {
      console.error('更新个人信息失败:', error)
      throw error
    }
  }

  // 获取订单列表
  const getOrders = async () => {
    try {
      // TODO: 替换为实际的API接口
      const response = await axios.get('/api/user/orders')
      return response.data
    } catch (error) {
      console.error('获取订单列表失败:', error)
      throw error
    }
  }

  // 获取地址列表
  const getAddresses = async () => {
    try {
      // TODO: 替换为实际的API接口
      const response = await axios.get('/api/user/addresses')
      return response.data
    } catch (error) {
      console.error('获取地址列表失败:', error)
      throw error
    }
  }

  // 添加地址
  const addAddress = async (addressData) => {
    try {
      // TODO: 替换为实际的API接口
      const response = await axios.post('/api/user/addresses', addressData)
      return response.data
    } catch (error) {
      console.error('添加地址失败:', error)
      throw error
    }
  }

  // 更新地址
  const updateAddress = async (addressId, addressData) => {
    try {
      // TODO: 替换为实际的API接口
      const response = await axios.put('/api/user/addresses/' + addressId, addressData)
      return response.data
    } catch (error) {
      console.error('更新地址失败:', error)
      throw error
    }
  }

  // 删除地址
  const deleteAddress = async (addressId) => {
    try {
      // TODO: 替换为实际的API接口
      await axios.delete('/api/user/addresses/' + addressId)
    } catch (error) {
      console.error('删除地址失败:', error)
      throw error
    }
  }

  // 初始化用户状态
  const initUserState = async () => {
    if (token.value) {
      try {
        // TODO: 替换为实际的API接口
        const response = await axios.get('/api/user/profile')
        const { user } = response.data

        username.value = user.username
        phone.value = user.phone
        avatar.value = user.avatar
        isLoggedIn.value = true

        axios.defaults.headers.common['Authorization'] = 'Bearer ' + token.value
      } catch (error) {
        console.error('获取用户信息失败:', error)
        logout()
      }
    }
  }

  return {
    token,
    username,
    phone,
    avatar,
    isLoggedIn,
    login,
    register,
    logout,
    updateProfile,
    getOrders,
    getAddresses,
    addAddress,
    updateAddress,
    deleteAddress,
    initUserState
  }
}) 
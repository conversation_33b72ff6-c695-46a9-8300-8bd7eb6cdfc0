import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login as apiLogin, logout as apiLogout, register as apiRegister, getUserInfo, updateUserInfo } from '../api/user'

export const useUserStore = defineStore('user', () => {
  // 用户状态
  const userId = ref(localStorage.getItem('userId') || '')
  const username = ref(localStorage.getItem('username') || '')
  const avatar = ref(localStorage.getItem('avatar') || '')
  const role = ref(localStorage.getItem('role') || '')
  const isLoggedIn = ref(!!localStorage.getItem('userId'))
  const loading = ref(false)

  // 登录
  const login = async (loginUsername, password) => {
    try {
      loading.value = true
      const result = await apiLogin(loginUsername, password)

      // 根据接口文档，登录成功返回用户信息字符串或用户ID
      // 这里假设返回的是用户ID，需要再获取用户详细信息
      if (result) {
        // 如果返回的是用户ID，获取用户详细信息
        let userInfo
        if (typeof result === 'string' && !isNaN(result)) {
          userInfo = await getUserInfo(parseInt(result))
        } else {
          // 如果直接返回用户信息
          userInfo = result
        }

        // 更新状态
        userId.value = userInfo.id.toString()
        username.value = userInfo.userName
        avatar.value = userInfo.avatar || ''
        role.value = userInfo.role || 'user'
        isLoggedIn.value = true

        // 保存到本地存储
        localStorage.setItem('userId', userId.value)
        localStorage.setItem('username', username.value)
        localStorage.setItem('avatar', avatar.value)
        localStorage.setItem('role', role.value)

        return userInfo
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData) => {
    try {
      loading.value = true
      const result = await apiRegister(userData)
      return result
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      // 调用后端登出接口
      await apiLogout()
    } catch (error) {
      console.error('登出接口调用失败:', error)
      // 即使接口失败也要清除本地状态
    } finally {
      // 清除本地状态
      userId.value = ''
      username.value = ''
      avatar.value = ''
      role.value = ''
      isLoggedIn.value = false

      // 清除本地存储
      localStorage.removeItem('userId')
      localStorage.removeItem('username')
      localStorage.removeItem('avatar')
      localStorage.removeItem('role')
    }
  }

  // 更新个人信息
  const updateProfile = async (profileData) => {
    try {
      loading.value = true
      const userInfo = {
        id: parseInt(userId.value),
        userName: profileData.userName || username.value,
        avatar: profileData.avatar || avatar.value,
        role: profileData.role || role.value
      }

      await updateUserInfo(userInfo)

      // 更新本地状态
      username.value = userInfo.userName
      avatar.value = userInfo.avatar
      role.value = userInfo.role

      // 更新本地存储
      localStorage.setItem('username', username.value)
      localStorage.setItem('avatar', avatar.value)
      localStorage.setItem('role', role.value)

      return userInfo
    } catch (error) {
      console.error('更新个人信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户信息
  const getCurrentUserInfo = async () => {
    try {
      if (!userId.value) {
        throw new Error('用户未登录')
      }
      loading.value = true
      const userInfo = await getUserInfo(parseInt(userId.value))

      // 更新本地状态
      username.value = userInfo.userName
      avatar.value = userInfo.avatar || ''
      role.value = userInfo.role || 'user'

      // 更新本地存储
      localStorage.setItem('username', username.value)
      localStorage.setItem('avatar', avatar.value)
      localStorage.setItem('role', role.value)

      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 初始化用户状态
  const initUserState = async () => {
    if (userId.value) {
      try {
        await getCurrentUserInfo()
      } catch (error) {
        console.error('初始化用户状态失败:', error)
        // 如果获取用户信息失败，清除登录状态
        await logout()
      }
    }
  }

  return {
    // 状态
    userId,
    username,
    avatar,
    role,
    isLoggedIn,
    loading,

    // 方法
    login,
    register,
    logout,
    updateProfile,
    getCurrentUserInfo,
    initUserState
  }
}) 
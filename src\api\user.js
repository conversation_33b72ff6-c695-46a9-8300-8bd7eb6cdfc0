import axios from 'axios'

const BASE_URL = 'http://localhost:8000'

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  withCredentials: true // 支持跨域携带cookie
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    // 统一处理响应格式
    if (data.code === 200) {
      return data.data
    } else {
      throw new Error(data.message || '请求失败')
    }
  },
  error => {
    console.error('用户API请求错误:', error)
    throw error
  }
)

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise<string>} 登录成功返回用户信息或token
 */
export const login = async (username, password) => {
  try {
    return await api.post('/user/login', null, {
      params: {
        username,
        password
      }
    })
  } catch (error) {
    console.error('用户登录失败:', error)
    throw error
  }
}

/**
 * 用户登出
 * @returns {Promise<void>}
 */
export const logout = async () => {
  try {
    return await api.post('/user/logout')
  } catch (error) {
    console.error('用户登出失败:', error)
    throw error
  }
}

/**
 * 用户注册
 * @param {Object} userInfo - 用户信息
 * @param {string} userInfo.userName - 用户名
 * @param {string} userInfo.password - 密码
 * @param {string} [userInfo.avatar] - 头像URL
 * @param {string} [userInfo.role] - 角色，默认为'user'
 * @returns {Promise<void>}
 */
export const register = async (userInfo) => {
  try {
    const userData = {
      userName: userInfo.userName,
      avatar: userInfo.avatar || '',
      role: userInfo.role || 'user'
    }
    return await api.post('/user/add', userData)
  } catch (error) {
    console.error('用户注册失败:', error)
    throw error
  }
}

/**
 * 获取用户信息
 * @param {number} userId - 用户ID
 * @returns {Promise<Object>} 用户信息
 */
export const getUserInfo = async (userId) => {
  try {
    return await api.get(`/user/detail/${userId}`)
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw error
  }
}

/**
 * 更新用户信息
 * @param {Object} userInfo - 用户信息
 * @param {number} userInfo.id - 用户ID
 * @param {string} [userInfo.userName] - 用户名
 * @param {string} [userInfo.avatar] - 头像URL
 * @param {string} [userInfo.role] - 角色
 * @returns {Promise<void>}
 */
export const updateUserInfo = async (userInfo) => {
  try {
    return await api.put('/user/update', userInfo)
  } catch (error) {
    console.error('更新用户信息失败:', error)
    throw error
  }
}

/**
 * 删除用户
 * @param {number} userId - 用户ID
 * @returns {Promise<void>}
 */
export const deleteUser = async (userId) => {
  try {
    return await api.delete(`/user/delete/${userId}`)
  } catch (error) {
    console.error('删除用户失败:', error)
    throw error
  }
}

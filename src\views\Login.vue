<template>
  <div class="login-page">
    <div class="login-container">
      <a-card class="login-card">
        <h2 class="title">{{ isLogin ? '登录' : '注册' }}</h2>
        
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          @finish="handleSubmit"
          layout="vertical"
        >
          <a-form-item
            name="username"
            label="用户名"
          >
            <a-input
              v-model:value="form.username"
              placeholder="请输入用户名"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            name="password"
            label="密码"
          >
            <a-input-password
              v-model:value="form.password"
              placeholder="请输入密码"
            >
              <template #prefix>
                <LockOutlined />
              </template>
            </a-input-password>
          </a-form-item>

          <template v-if="!isLogin">
            <a-form-item
              name="confirmPassword"
              label="确认密码"
            >
              <a-input-password
                v-model:value="form.confirmPassword"
                placeholder="请确认密码"
              >
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input-password>
            </a-form-item>

            <a-form-item
              name="avatar"
              label="头像URL（可选）"
            >
              <a-input
                v-model:value="form.avatar"
                placeholder="请输入头像URL"
              >
                <template #prefix>
                  <UserOutlined />
                </template>
              </a-input>
            </a-form-item>
          </template>

          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              :loading="loading"
              block
            >
              {{ isLogin ? '登录' : '注册' }}
            </a-button>
          </a-form-item>

          <div class="form-footer">
            <a-button type="link" @click="toggleMode">
              {{ isLogin ? '没有账号？立即注册' : '已有账号？立即登录' }}
            </a-button>
          </div>
        </a-form>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { useCartStore } from '../stores/cart'
import { message } from 'ant-design-vue'
import {
  UserOutlined,
  LockOutlined,
  PhoneOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const cartStore = useCartStore()
const formRef = ref(null)
const isLogin = ref(true)
const loading = ref(false)

const form = ref({
  username: '',
  password: '',
  confirmPassword: '',
  avatar: ''
})

const validateConfirmPassword = async (rule, value) => {
  if (value && value !== form.value.password) {
    throw new Error('两次输入密码不一致')
  }
}

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur', validateTrigger: ['blur'] },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  avatar: [
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ]
}

const handleSubmit = async () => {
  try {
    loading.value = true

    if (isLogin.value) {
      // 登录
      await userStore.login(form.value.username, form.value.password)
      message.success('登录成功')

      // 登录成功后同步本地购物车到服务器
      try {
        await cartStore.syncLocalCartToServer()
      } catch (error) {
        console.error('同步购物车失败:', error)
      }

      // 登录成功后跳转到之前的页面或首页
      const redirect = router.currentRoute.value.query.redirect || '/'
      router.push(redirect)
    } else {
      // 注册
      const userData = {
        userName: form.value.username,
        avatar: form.value.avatar || '',
        role: 'user'
      }
      await userStore.register(userData)
      message.success('注册成功，请登录')
      isLogin.value = true
      // 清空表单
      form.value = {
        username: '',
        password: '',
        confirmPassword: '',
        avatar: ''
      }
    }
  } catch (error) {
    console.error('操作失败:', error)
    message.error(error.message || '操作失败，请重试')
  } finally {
    loading.value = false
  }
}

const toggleMode = () => {
  isLogin.value = !isLogin.value
  form.value = {
    username: '',
    password: '',
    confirmPassword: '',
    avatar: ''
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  padding: 24px;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.title {
  text-align: center;
  margin-bottom: 32px;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 500;
  font-size: 24px;
}

.form-footer {
  margin-top: 16px;
  text-align: center;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

:deep(.ant-input-affix-wrapper) {
  padding: 8px 11px;
}

:deep(.ant-btn-link) {
  padding: 4px 8px;
  height: auto;
}
</style> 
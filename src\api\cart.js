import axios from 'axios'
import { processApiResponseIds, COMMON_ID_FIELDS } from '../utils/bigint'

const BASE_URL = 'http://localhost:8000'

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  withCredentials: true // 支持跨域携带cookie
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加token认证信息
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    // 统一处理响应格式
    if (data.code === 200) {
      // 处理响应数据中的大整数ID，避免精度丢失
      const processedData = processApiResponseIds(data, COMMON_ID_FIELDS)
      return processedData
    } else {
      throw new Error(data.message || '请求失败')
    }
  },
  error => {
    console.error('购物车API请求错误:', error)

    // 处理401未授权错误
    if (error.response && error.response.status === 401) {
      // 清除本地token
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('username')
      localStorage.removeItem('avatar')
      localStorage.removeItem('role')

      // 跳转到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/login'
      }
    }

    throw error
  }
)

/**
 * 添加商品到购物车
 * @param {Object} cartItem - 购物车项信息
 * @param {string|number} cartItem.userId - 用户ID（支持字符串格式以避免大整数精度丢失）
 * @param {string|number} cartItem.productId - 商品ID（支持字符串格式以避免大整数精度丢失）
 * @param {string} [cartItem.specModel] - 商品规格
 * @param {number} cartItem.quantity - 商品数量
 * @returns {Promise<string>} 添加结果
 */
export const addToCart = async (cartItem) => {
  try {
    const cartData = {
      userId: cartItem.userId,
      productId: cartItem.productId,
      specModel: cartItem.specModel || '',
      quantity: cartItem.quantity
    }
    return await api.post('/cart/add', cartData)
  } catch (error) {
    console.error('添加到购物车失败:', error)
    throw error
  }
}

/**
 * 从购物车移除商品
 * @param {string|number} cartItemId - 购物车项ID（支持字符串格式以避免大整数精度丢失）
 * @returns {Promise<string>} 移除结果
 */
export const removeFromCart = async (cartItemId) => {
  try {
    return await api.put('/cart/remove', null, {
      params: {
        id: cartItemId
      }
    })
  } catch (error) {
    console.error('从购物车移除商品失败:', error)
    throw error
  }
}

/**
 * 获取购物车列表
 * @returns {Promise<Array>} 购物车列表
 */
export const getCartList = async () => {
  try {
    return await api.get('/cart/list')
  } catch (error) {
    console.error('获取购物车列表失败:', error)
    throw error
  }
}

/**
 * 分页获取购物车列表
 * @param {number} [page=1] - 页码
 * @param {number} [limit=10] - 每页数量
 * @returns {Promise<Object>} 分页购物车数据
 */
export const getCartPageList = async (page = 1, limit = 10) => {
  try {
    return await api.get('/cart/page', {
      params: {
        page,
        limit
      }
    })
  } catch (error) {
    console.error('分页获取购物车列表失败:', error)
    throw error
  }
}

/**
 * 更新购物车商品数量
 * 注意：根据接口文档，没有直接的更新数量接口，需要先删除再添加
 * @param {string|number} cartItemId - 购物车项ID（支持字符串格式以避免大整数精度丢失）
 * @param {Object} newCartItem - 新的购物车项信息
 * @param {string|number} newCartItem.userId - 用户ID（支持字符串格式以避免大整数精度丢失）
 * @param {string|number} newCartItem.productId - 商品ID（支持字符串格式以避免大整数精度丢失）
 * @param {string} [newCartItem.specModel] - 商品规格
 * @param {number} newCartItem.quantity - 新的商品数量
 * @returns {Promise<string>} 更新结果
 */
export const updateCartItemQuantity = async (cartItemId, newCartItem) => {
  try {
    // 先删除原有项
    await removeFromCart(cartItemId)
    // 再添加新项
    return await addToCart(newCartItem)
  } catch (error) {
    console.error('更新购物车商品数量失败:', error)
    throw error
  }
}

/**
 * 清空购物车（批量删除）
 * 注意：接口文档中没有清空购物车的接口，需要逐个删除
 * @param {Array<string|number>} cartItemIds - 购物车项ID数组（支持字符串格式以避免大整数精度丢失）
 * @returns {Promise<Array>} 删除结果数组
 */
export const clearCart = async (cartItemIds) => {
  try {
    const promises = cartItemIds.map(id => removeFromCart(id))
    return await Promise.all(promises)
  } catch (error) {
    console.error('清空购物车失败:', error)
    throw error
  }
}

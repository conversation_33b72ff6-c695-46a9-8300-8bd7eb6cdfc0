/**
 * 大整数处理工具函数
 * 用于解决JavaScript大整数精度丢失问题
 */

/**
 * 安全地将值转换为字符串ID
 * @param {string|number|bigint} value - 要转换的值
 * @returns {string} 字符串格式的ID
 */
export const toSafeId = (value) => {
  if (value === null || value === undefined) {
    return ''
  }
  return String(value)
}

/**
 * 检查是否为有效的ID
 * @param {string|number} id - 要检查的ID
 * @returns {boolean} 是否为有效ID
 */
export const isValidId = (id) => {
  if (!id) return false
  const str = String(id)
  return /^\d+$/.test(str) && str.length > 0
}

/**
 * 比较两个ID是否相等
 * @param {string|number} id1 - 第一个ID
 * @param {string|number} id2 - 第二个ID
 * @returns {boolean} 是否相等
 */
export const compareIds = (id1, id2) => {
  return String(id1) === String(id2)
}

/**
 * 从JSON响应中安全地提取ID
 * 处理JSON.parse可能导致的精度丢失问题
 * @param {string} jsonString - JSON字符串
 * @param {string} idField - ID字段名
 * @returns {string} 安全的ID字符串
 */
export const extractIdFromJson = (jsonString, idField = 'id') => {
  try {
    // 使用正则表达式直接提取ID字符串，避免JSON.parse的精度丢失
    const regex = new RegExp(`"${idField}"\\s*:\\s*"?([0-9]+)"?`)
    const match = jsonString.match(regex)
    return match ? match[1] : null
  } catch (error) {
    console.error('提取ID失败:', error)
    return null
  }
}

/**
 * 安全地处理API响应中的ID字段
 * @param {Object} data - API响应数据
 * @param {Array<string>} idFields - 需要处理的ID字段名数组
 * @returns {Object} 处理后的数据
 */
export const processApiResponseIds = (data, idFields = ['id', 'userId', 'productId']) => {
  if (!data || typeof data !== 'object') {
    return data
  }

  const processed = { ...data }
  
  idFields.forEach(field => {
    if (processed[field] !== undefined) {
      processed[field] = toSafeId(processed[field])
    }
  })

  // 如果是数组，递归处理每个元素
  if (Array.isArray(processed)) {
    return processed.map(item => processApiResponseIds(item, idFields))
  }

  return processed
}

/**
 * 检查数字是否超过JavaScript安全整数范围
 * @param {string|number} value - 要检查的值
 * @returns {boolean} 是否超过安全范围
 */
export const isUnsafeInteger = (value) => {
  const num = Number(value)
  return !Number.isSafeInteger(num)
}

/**
 * 安全地格式化ID用于显示
 * @param {string|number} id - ID值
 * @param {string} prefix - 前缀（可选）
 * @returns {string} 格式化后的ID
 */
export const formatIdForDisplay = (id, prefix = '') => {
  const safeId = toSafeId(id)
  return prefix ? `${prefix}${safeId}` : safeId
}

/**
 * 验证ID格式是否正确
 * @param {string|number} id - 要验证的ID
 * @param {Object} options - 验证选项
 * @param {number} options.minLength - 最小长度
 * @param {number} options.maxLength - 最大长度
 * @returns {Object} 验证结果
 */
export const validateId = (id, options = {}) => {
  const { minLength = 1, maxLength = 20 } = options
  const safeId = toSafeId(id)
  
  const result = {
    isValid: true,
    errors: []
  }

  if (!safeId) {
    result.isValid = false
    result.errors.push('ID不能为空')
    return result
  }

  if (!/^\d+$/.test(safeId)) {
    result.isValid = false
    result.errors.push('ID只能包含数字')
  }

  if (safeId.length < minLength) {
    result.isValid = false
    result.errors.push(`ID长度不能少于${minLength}位`)
  }

  if (safeId.length > maxLength) {
    result.isValid = false
    result.errors.push(`ID长度不能超过${maxLength}位`)
  }

  return result
}

// 常用的ID字段名
export const COMMON_ID_FIELDS = [
  'id',
  'userId', 
  'productId',
  'cartId',
  'orderId',
  'categoryId',
  'createdBy',
  'updatedBy'
]

// JavaScript安全整数范围
export const SAFE_INTEGER_RANGE = {
  MIN: Number.MIN_SAFE_INTEGER,
  MAX: Number.MAX_SAFE_INTEGER
}

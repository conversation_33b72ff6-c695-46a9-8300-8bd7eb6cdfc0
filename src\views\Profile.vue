<template>
  <div class="profile-page">
    <header class="header">
      <div class="header-content">
        <el-button icon="Back" @click="$router.back()">返回</el-button>
        <h1 class="page-title">个人中心</h1>
      </div>
    </header>

    <div class="profile-container">
      <el-card class="profile-card">
        <template #header>
          <div class="profile-header">
            <div class="avatar-container">
              <el-avatar :size="80" :src="userStore.avatar">
                {{ userStore.username?.charAt(0).toUpperCase() }}
              </el-avatar>
            </div>
            <div class="user-info">
              <h2>{{ userStore.username }}</h2>
              <p>{{ userStore.phone }}</p>
            </div>
          </div>
        </template>

        <el-tabs v-model="activeTab">
          <el-tab-pane label="账号信息" name="account">
            <el-form
              ref="formRef"
              :model="form"
              :rules="rules"
              label-width="100px"
            >
              <el-form-item label="用户名" prop="username">
                <el-input v-model="form.username" />
              </el-form-item>

              <el-form-item label="手机号" prop="phone">
                <el-input v-model="form.phone" />
              </el-form-item>

              <el-form-item label="新密码" prop="newPassword">
                <el-input
                  v-model="form.newPassword"
                  type="password"
                  show-password
                  placeholder="不修改请留空"
                />
              </el-form-item>

              <el-form-item label="确认新密码" prop="confirmPassword">
                <el-input
                  v-model="form.confirmPassword"
                  type="password"
                  show-password
                  placeholder="不修改请留空"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="updateProfile"
                  :loading="loading"
                >
                  保存修改
                </el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <el-tab-pane label="我的订单" name="orders">
            <el-table :data="orders" style="width: 100%">
              <el-table-column prop="id" label="订单号" width="180" />
              <el-table-column prop="date" label="下单时间" width="180" />
              <el-table-column prop="total" label="金额" width="100">
                <template #default="scope">
                  ¥{{ scope.row.total.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态">
                <template #default="scope">
                  <el-tag :type="getOrderStatusType(scope.row.status)">
                    {{ getOrderStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    @click="viewOrderDetail(scope.row)"
                  >
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="收货地址" name="addresses">
            <div class="address-list">
              <el-button
                type="primary"
                icon="Plus"
                @click="showAddressDialog()"
                class="add-address-btn"
              >
                新增地址
              </el-button>

              <el-card
                v-for="address in addresses"
                :key="address.id"
                class="address-item"
              >
                <div class="address-content">
                  <div class="address-info">
                    <h3>{{ address.name }} {{ address.phone }}</h3>
                    <p>{{ address.province }}{{ address.city }}{{ address.district }}{{ address.detail }}</p>
                  </div>
                  <div class="address-actions">
                    <el-button
                      link
                      type="primary"
                      @click="showAddressDialog(address)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      link
                      type="danger"
                      @click="deleteAddress(address)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 地址编辑对话框 -->
    <el-dialog
      v-model="addressDialogVisible"
      :title="editingAddress ? '编辑地址' : '新增地址'"
      width="500px"
    >
      <el-form
        ref="addressFormRef"
        :model="addressForm"
        :rules="addressRules"
        label-width="100px"
      >
        <el-form-item label="收货人" prop="name">
          <el-input v-model="addressForm.name" />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addressForm.phone" />
        </el-form-item>

        <el-form-item label="所在地区" required>
          <el-cascader
            v-model="addressForm.region"
            :options="regionOptions"
            placeholder="请选择省/市/区"
          />
        </el-form-item>

        <el-form-item label="详细地址" prop="detail">
          <el-input
            v-model="addressForm.detail"
            type="textarea"
            rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addressDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAddress">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '../stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const userStore = useUserStore()
const activeTab = ref('account')
const loading = ref(false)
const formRef = ref(null)
const addressFormRef = ref(null)

// 表单数据
const form = ref({
  username: userStore.username,
  phone: userStore.phone,
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  newPassword: [
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (form.value.newPassword && value !== form.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 更新个人信息
const updateProfile = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // TODO: 调用API更新用户信息
    await userStore.updateProfile(form.value)
    ElMessage.success('更新成功')

    // 清空密码字段
    form.value.newPassword = ''
    form.value.confirmPassword = ''
  } catch (error) {
    console.error('更新失败:', error)
  } finally {
    loading.value = false
  }
}

// 订单相关
const orders = ref([])
const loadOrders = async () => {
  try {
    // TODO: 调用API获取订单列表
    const response = await userStore.getOrders()
    orders.value = response.data
  } catch (error) {
    console.error('获取订单失败:', error)
  }
}

const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    shipped: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待付款',
    paid: '已付款',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const viewOrderDetail = (order) => {
  // TODO: 实现查看订单详情
  console.log('查看订单详情:', order)
}

// 地址相关
const addresses = ref([])
const addressDialogVisible = ref(false)
const editingAddress = ref(null)
const addressForm = ref({
  name: '',
  phone: '',
  region: [],
  detail: ''
})

const addressRules = {
  name: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  detail: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ]
}

// 省市区数据（示例）
const regionOptions = [
  {
    value: '浙江',
    label: '浙江省',
    children: [
      {
        value: '杭州',
        label: '杭州市',
        children: [
          { value: '西湖', label: '西湖区' },
          { value: '余杭', label: '余杭区' }
        ]
      }
    ]
  }
  // 更多省市区数据...
]

const showAddressDialog = (address = null) => {
  editingAddress.value = address
  if (address) {
    addressForm.value = {
      name: address.name,
      phone: address.phone,
      region: [address.province, address.city, address.district],
      detail: address.detail
    }
  } else {
    addressForm.value = {
      name: '',
      phone: '',
      region: [],
      detail: ''
    }
  }
  addressDialogVisible.value = true
}

const saveAddress = async () => {
  if (!addressFormRef.value) return

  try {
    await addressFormRef.value.validate()
    
    const addressData = {
      name: addressForm.value.name,
      phone: addressForm.value.phone,
      province: addressForm.value.region[0],
      city: addressForm.value.region[1],
      district: addressForm.value.region[2],
      detail: addressForm.value.detail
    }

    if (editingAddress.value) {
      // TODO: 调用API更新地址
      await userStore.updateAddress(editingAddress.value.id, addressData)
    } else {
      // TODO: 调用API新增地址
      await userStore.addAddress(addressData)
    }

    ElMessage.success(editingAddress.value ? '更新成功' : '添加成功')
    addressDialogVisible.value = false
    loadAddresses()
  } catch (error) {
    console.error('保存地址失败:', error)
  }
}

const deleteAddress = async (address) => {
  try {
    await ElMessageBox.confirm('确定要删除该地址吗？', '提示', {
      type: 'warning'
    })
    // TODO: 调用API删除地址
    await userStore.deleteAddress(address.id)
    ElMessage.success('删除成功')
    loadAddresses()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除地址失败:', error)
    }
  }
}

const loadAddresses = async () => {
  try {
    // TODO: 调用API获取地址列表
    const response = await userStore.getAddresses()
    addresses.value = response.data
  } catch (error) {
    console.error('获取地址列表失败:', error)
  }
}

onMounted(() => {
  loadOrders()
  loadAddresses()
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 60px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
}

.profile-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
}

.profile-card {
  margin-bottom: 20px;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
}

.user-info h2 {
  margin: 0 0 8px;
  font-size: 20px;
}

.user-info p {
  margin: 0;
  color: #666;
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.add-address-btn {
  align-self: flex-start;
}

.address-item {
  margin-bottom: 16px;
}

.address-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.address-info h3 {
  margin: 0 0 8px;
  font-size: 16px;
}

.address-info p {
  margin: 0;
  color: #666;
}

.address-actions {
  display: flex;
  gap: 8px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}
</style> 
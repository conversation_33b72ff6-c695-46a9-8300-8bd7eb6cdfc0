<template>
  <div class="profile-page">
    <div class="profile-container">
      <div class="profile-header">
        <h1 class="page-title">个人中心</h1>
        <a-button @click="$router.back()" type="text">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
      </div>

      <!-- 用户信息卡片 -->
      <a-card class="user-info-card" :loading="userStore.loading">
        <div class="user-info-header">
          <div class="avatar-section">
            <a-avatar :size="80" :src="userStore.avatar">
              {{ userStore.username?.charAt(0).toUpperCase() }}
            </a-avatar>
            <a-upload
              :show-upload-list="false"
              :before-upload="beforeAvatarUpload"
              @change="handleAvatarChange"
              accept="image/*"
            >
              <a-button type="link" size="small">
                <template #icon>
                  <CameraOutlined />
                </template>
                更换头像
              </a-button>
            </a-upload>
          </div>
          <div class="user-basic-info">
            <h2>{{ userStore.username }}</h2>
            <p class="user-role">{{ userStore.role === 'admin' ? '管理员' : '普通用户' }}</p>
            <p class="user-id">用户ID: {{ userStore.userId }}</p>
          </div>
        </div>
      </a-card>

      <!-- 功能选项卡 -->
      <a-card class="profile-tabs-card">
        <a-tabs v-model:activeKey="activeTab" type="card">
          <a-tab-pane key="account" tab="账号信息">
            <a-form
              ref="formRef"
              :model="form"
              :rules="rules"
              layout="vertical"
              @finish="handleUpdateProfile"
            >
              <a-form-item label="用户名" name="userName">
                <a-input v-model:value="form.userName" placeholder="请输入用户名" />
              </a-form-item>

              <a-form-item label="头像URL" name="avatar">
                <a-input v-model:value="form.avatar" placeholder="请输入头像URL" />
              </a-form-item>

              <a-form-item label="角色" name="role">
                <a-select v-model:value="form.role" disabled>
                  <a-select-option value="user">普通用户</a-select-option>
                  <a-select-option value="admin">管理员</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item>
                <a-space>
                  <a-button
                    type="primary"
                    html-type="submit"
                    :loading="loading"
                  >
                    保存修改
                  </a-button>
                  <a-button @click="resetForm">
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-tab-pane>

          <a-tab-pane key="security" tab="账号安全">
            <div class="security-section">
              <a-list>
                <a-list-item>
                  <a-list-item-meta
                    title="登录密码"
                    description="定期更换密码可以保护账号安全"
                  />
                  <a-button type="link">修改密码</a-button>
                </a-list-item>
                <a-list-item>
                  <a-list-item-meta
                    title="退出登录"
                    description="退出当前账号，清除登录状态"
                  />
                  <a-button type="link" danger @click="handleLogout">
                    退出登录
                  </a-button>
                </a-list-item>
              </a-list>
            </div>
          </a-tab-pane>

          <a-tab-pane key="about" tab="关于">
            <div class="about-section">
              <a-descriptions title="应用信息" bordered>
                <a-descriptions-item label="应用名称">8号商城</a-descriptions-item>
                <a-descriptions-item label="版本">v1.0.0</a-descriptions-item>
                <a-descriptions-item label="技术栈">Vue 3 + Ant Design Vue</a-descriptions-item>
                <a-descriptions-item label="开发者">运动鞋商城团队</a-descriptions-item>
              </a-descriptions>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </div>
</template>



<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { message, Modal } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  CameraOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const activeTab = ref('account')
const loading = ref(false)
const formRef = ref(null)

// 表单数据
const form = ref({
  userName: userStore.username,
  avatar: userStore.avatar,
  role: userStore.role
})

// 表单验证规则
const rules = {
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  avatar: [
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ]
}

// 处理表单提交
const handleUpdateProfile = async (values) => {
  try {
    loading.value = true
    await userStore.updateProfile(values)
    message.success('更新成功')

    // 更新表单数据
    form.value = {
      userName: userStore.username,
      avatar: userStore.avatar,
      role: userStore.role
    }
  } catch (error) {
    console.error('更新失败:', error)
    message.error('更新失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    userName: userStore.username,
    avatar: userStore.avatar,
    role: userStore.role
  }
}

// 处理头像上传前的验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    message.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  return false // 阻止自动上传，这里只是演示
}

// 处理头像变化
const handleAvatarChange = (info) => {
  // TODO: 实现头像上传逻辑
  message.info('头像上传功能开发中...')
}

// 处理登出
const handleLogout = () => {
  Modal.confirm({
    title: '确认登出',
    content: '确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await userStore.logout()
        message.success('已退出登录')
        router.push('/login')
      } catch (error) {
        console.error('登出失败:', error)
        message.error('登出失败')
      }
    }
  })
}

// 组件挂载时初始化表单数据
onMounted(() => {
  form.value = {
    userName: userStore.username,
    avatar: userStore.avatar,
    role: userStore.role
  }
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.user-info-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.user-info-header {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.user-basic-info h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.user-role {
  margin: 0 0 4px 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}

.user-id {
  margin: 0;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.profile-tabs-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.security-section,
.about-section {
  padding: 16px 0;
}

:deep(.ant-tabs-card .ant-tabs-tab) {
  border-radius: 6px 6px 0 0;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

@media (max-width: 768px) {
  .profile-page {
    padding: 16px;
  }

  .user-info-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .profile-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>
<template>
  <div class="profile-page">
    <div class="profile-container">
      <div class="profile-header">
        <h1 class="page-title">个人中心</h1>
        <a-button @click="$router.back()" type="text">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
      </div>

      <!-- 用户信息卡片 -->
      <a-card class="user-info-card" :loading="userStore.loading">
        <div class="user-info-header">
          <div class="avatar-section">
            <a-avatar :size="80" :src="userStore.avatar">
              {{ userStore.username?.charAt(0).toUpperCase() }}
            </a-avatar>
            <a-upload
              :show-upload-list="false"
              :before-upload="beforeAvatarUpload"
              @change="handleAvatarChange"
              accept="image/*"
            >
              <a-button type="link" size="small">
                <template #icon>
                  <CameraOutlined />
                </template>
                更换头像
              </a-button>
            </a-upload>
          </div>
          <div class="user-basic-info">
            <h2>{{ userStore.username }}</h2>
            <p class="user-role">{{ userStore.role === 'admin' ? '管理员' : '普通用户' }}</p>
            <p class="user-id">用户ID: {{ userStore.userId }}</p>
          </div>
        </div>
      </a-card>

      <!-- 功能选项卡 -->
      <a-card class="profile-tabs-card">
        <a-tabs v-model:activeKey="activeTab" type="card">
          <a-tab-pane key="account" tab="账号信息">
            <a-form
              ref="formRef"
              :model="form"
              :rules="rules"
              layout="vertical"
              @finish="handleUpdateProfile"
            >
              <a-form-item label="用户名" name="userName">
                <a-input v-model:value="form.userName" placeholder="请输入用户名" />
              </a-form-item>

              <a-form-item label="头像URL" name="avatar">
                <a-input v-model:value="form.avatar" placeholder="请输入头像URL" />
              </a-form-item>

              <a-form-item label="角色" name="role">
                <a-select v-model:value="form.role" disabled>
                  <a-select-option value="user">普通用户</a-select-option>
                  <a-select-option value="admin">管理员</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item>
                <a-space>
                  <a-button
                    type="primary"
                    html-type="submit"
                    :loading="loading"
                  >
                    保存修改
                  </a-button>
                  <a-button @click="resetForm">
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-tab-pane>

          <a-tab-pane key="security" tab="账号安全">
            <div class="security-section">
              <a-list>
                <a-list-item>
                  <a-list-item-meta
                    title="登录密码"
                    description="定期更换密码可以保护账号安全"
                  />
                  <a-button type="link">修改密码</a-button>
                </a-list-item>
                <a-list-item>
                  <a-list-item-meta
                    title="退出登录"
                    description="退出当前账号，清除登录状态"
                  />
                  <a-button type="link" danger @click="handleLogout">
                    退出登录
                  </a-button>
                </a-list-item>
              </a-list>
            </div>
          </a-tab-pane>

          <a-tab-pane key="about" tab="关于">
            <div class="about-section">
              <a-descriptions title="应用信息" bordered>
                <a-descriptions-item label="应用名称">8号商城</a-descriptions-item>
                <a-descriptions-item label="版本">v1.0.0</a-descriptions-item>
                <a-descriptions-item label="技术栈">Vue 3 + Ant Design Vue</a-descriptions-item>
                <a-descriptions-item label="开发者">运动鞋商城团队</a-descriptions-item>
              </a-descriptions>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </div>
</template>

          <el-tab-pane label="收货地址" name="addresses">
            <div class="address-list">
              <el-button
                type="primary"
                icon="Plus"
                @click="showAddressDialog()"
                class="add-address-btn"
              >
                新增地址
              </el-button>

              <el-card
                v-for="address in addresses"
                :key="address.id"
                class="address-item"
              >
                <div class="address-content">
                  <div class="address-info">
                    <h3>{{ address.name }} {{ address.phone }}</h3>
                    <p>{{ address.province }}{{ address.city }}{{ address.district }}{{ address.detail }}</p>
                  </div>
                  <div class="address-actions">
                    <el-button
                      link
                      type="primary"
                      @click="showAddressDialog(address)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      link
                      type="danger"
                      @click="deleteAddress(address)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 地址编辑对话框 -->
    <el-dialog
      v-model="addressDialogVisible"
      :title="editingAddress ? '编辑地址' : '新增地址'"
      width="500px"
    >
      <el-form
        ref="addressFormRef"
        :model="addressForm"
        :rules="addressRules"
        label-width="100px"
      >
        <el-form-item label="收货人" prop="name">
          <el-input v-model="addressForm.name" />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addressForm.phone" />
        </el-form-item>

        <el-form-item label="所在地区" required>
          <el-cascader
            v-model="addressForm.region"
            :options="regionOptions"
            placeholder="请选择省/市/区"
          />
        </el-form-item>

        <el-form-item label="详细地址" prop="detail">
          <el-input
            v-model="addressForm.detail"
            type="textarea"
            rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addressDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAddress">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '../stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const userStore = useUserStore()
const activeTab = ref('account')
const loading = ref(false)
const formRef = ref(null)
const addressFormRef = ref(null)

// 表单数据
const form = ref({
  username: userStore.username,
  phone: userStore.phone,
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  newPassword: [
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (form.value.newPassword && value !== form.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 更新个人信息
const updateProfile = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // TODO: 调用API更新用户信息
    await userStore.updateProfile(form.value)
    ElMessage.success('更新成功')

    // 清空密码字段
    form.value.newPassword = ''
    form.value.confirmPassword = ''
  } catch (error) {
    console.error('更新失败:', error)
  } finally {
    loading.value = false
  }
}

// 订单相关
const orders = ref([])
const loadOrders = async () => {
  try {
    // TODO: 调用API获取订单列表
    const response = await userStore.getOrders()
    orders.value = response.data
  } catch (error) {
    console.error('获取订单失败:', error)
  }
}

const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    shipped: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待付款',
    paid: '已付款',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const viewOrderDetail = (order) => {
  // TODO: 实现查看订单详情
  console.log('查看订单详情:', order)
}

// 地址相关
const addresses = ref([])
const addressDialogVisible = ref(false)
const editingAddress = ref(null)
const addressForm = ref({
  name: '',
  phone: '',
  region: [],
  detail: ''
})

const addressRules = {
  name: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  detail: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ]
}

// 省市区数据（示例）
const regionOptions = [
  {
    value: '浙江',
    label: '浙江省',
    children: [
      {
        value: '杭州',
        label: '杭州市',
        children: [
          { value: '西湖', label: '西湖区' },
          { value: '余杭', label: '余杭区' }
        ]
      }
    ]
  }
  // 更多省市区数据...
]

const showAddressDialog = (address = null) => {
  editingAddress.value = address
  if (address) {
    addressForm.value = {
      name: address.name,
      phone: address.phone,
      region: [address.province, address.city, address.district],
      detail: address.detail
    }
  } else {
    addressForm.value = {
      name: '',
      phone: '',
      region: [],
      detail: ''
    }
  }
  addressDialogVisible.value = true
}

const saveAddress = async () => {
  if (!addressFormRef.value) return

  try {
    await addressFormRef.value.validate()
    
    const addressData = {
      name: addressForm.value.name,
      phone: addressForm.value.phone,
      province: addressForm.value.region[0],
      city: addressForm.value.region[1],
      district: addressForm.value.region[2],
      detail: addressForm.value.detail
    }

    if (editingAddress.value) {
      // TODO: 调用API更新地址
      await userStore.updateAddress(editingAddress.value.id, addressData)
    } else {
      // TODO: 调用API新增地址
      await userStore.addAddress(addressData)
    }

    ElMessage.success(editingAddress.value ? '更新成功' : '添加成功')
    addressDialogVisible.value = false
    loadAddresses()
  } catch (error) {
    console.error('保存地址失败:', error)
  }
}

const deleteAddress = async (address) => {
  try {
    await ElMessageBox.confirm('确定要删除该地址吗？', '提示', {
      type: 'warning'
    })
    // TODO: 调用API删除地址
    await userStore.deleteAddress(address.id)
    ElMessage.success('删除成功')
    loadAddresses()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除地址失败:', error)
    }
  }
}

const loadAddresses = async () => {
  try {
    // TODO: 调用API获取地址列表
    const response = await userStore.getAddresses()
    addresses.value = response.data
  } catch (error) {
    console.error('获取地址列表失败:', error)
  }
}

onMounted(() => {
  loadOrders()
  loadAddresses()
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 60px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
}

.profile-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
}

.profile-card {
  margin-bottom: 20px;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
}

.user-info h2 {
  margin: 0 0 8px;
  font-size: 20px;
}

.user-info p {
  margin: 0;
  color: #666;
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.add-address-btn {
  align-self: flex-start;
}

.address-item {
  margin-bottom: 16px;
}

.address-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.address-info h3 {
  margin: 0 0 8px;
  font-size: 16px;
}

.address-info p {
  margin: 0;
  color: #666;
}

.address-actions {
  display: flex;
  gap: 8px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}
</style> 
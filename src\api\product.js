import axios from 'axios'

const BASE_URL = 'http://localhost:8000'

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  withCredentials: true // 支持跨域携带cookie
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    // 统一处理响应格式
    if (data.code === 200) {
      return data.data
    } else {
      throw new Error(data.message || '请求失败')
    }
  },
  error => {
    console.error('API请求错误:', error)
    throw error
  }
)

// 获取商品列表
export const getProductList = async (page = 1, limit = 20, q = '') => {
  try {
    return await api.get('/product/list', {
      params: {
        page,
        limit,
        q
      }
    })
  } catch (error) {
    console.error('获取商品列表失败:', error)
    throw error
  }
}

// 获取商品详情
export const getProductDetail = async (id) => {
  try {
    return await api.get(`/product/detail/${id}`)
  } catch (error) {
    console.error('获取商品详情失败:', error)
    throw error
  }
}
import axios from 'axios'

const BASE_URL = 'http://localhost:8000'

// 获取商品列表
export const getProductList = async (page = 1, limit = 20, q = '') => {
  try {
    const response = await axios.get(`${BASE_URL}/product/list`, {
      params: {
        page,
        limit,
        q
      }
    })
    return response.data
  } catch (error) {
    console.error('获取商品列表失败:', error)
    throw error
  }
}

// 获取商品详情
export const getProductDetail = async (id) => {
  try {
    const response = await axios.get(`${BASE_URL}/product/detail/${id}`)
    return response.data
  } catch (error) {
    console.error('获取商品详情失败:', error)
    throw error
  }
} 
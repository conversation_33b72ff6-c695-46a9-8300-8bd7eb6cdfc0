<script setup>
import { ref, onMounted, provide, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from './stores/user'
import { useCartStore } from './stores/cart'
import { 
  SearchOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons-vue'
import { debounce } from 'lodash-es'

const router = useRouter()
const userStore = useUserStore()
const cartStore = useCartStore()
const searchQuery = ref('')
const showHistory = ref(false)
const searchHistory = ref([])

const HISTORY_KEY = 'search_history'
const MAX_HISTORY = 10

// 添加点击外部区域关闭搜索历史的处理函数
const handleClickOutside = (e) => {
  const searchContainer = document.querySelector('.search-container')
  if (searchContainer && !searchContainer.contains(e.target)) {
    showHistory.value = false
  }
}

const handleSearch = () => {
  const query = searchQuery.value.trim()
  if (query) {
    saveSearchHistory(query)
  }
  // 直接打开新标签页展示搜索结果
  const baseUrl = window.location.origin
  window.open(`${baseUrl}/product/list?q=${encodeURIComponent(query)}`, '_blank')
}

const handleLogout = () => {
  userStore.logout()
  router.push('/')
}

const loadSearchHistory = () => {
  const history = localStorage.getItem(HISTORY_KEY)
  searchHistory.value = history ? JSON.parse(history) : []
}

const saveSearchHistory = (keyword) => {
  if (!keyword) return
  const history = searchHistory.value
  const index = history.indexOf(keyword)
  if (index > -1) {
    history.splice(index, 1)
  }
  history.unshift(keyword)
  if (history.length > MAX_HISTORY) {
    history.pop()
  }
  searchHistory.value = history
  localStorage.setItem(HISTORY_KEY, JSON.stringify(history))
}

const clearHistory = () => {
  searchHistory.value = []
  localStorage.removeItem(HISTORY_KEY)
}

const useHistoryItem = (keyword) => {
  searchQuery.value = keyword
  handleSearch()
}

const handleKeyDown = (e) => {
  if (e.key === 'Enter') {
    handleSearch()
  }
}

onMounted(() => {
  userStore.initUserState()
  loadSearchHistory()
  // 添加全局点击事件监听
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // 移除全局点击事件监听
  document.removeEventListener('click', handleClickOutside)
})

provide('searchKeyword', searchQuery)
</script>

<template>
  <a-layout class="layout">
    <a-layout-header class="header">
      <div class="nav-container">
        <div class="nav-left">
          <router-link to="/" class="logo">
            <img src="./assets/logo.png" alt="8号商城" />8号商城
          </router-link>
        </div>

        <div class="nav-center">
          <div class="search-container" @click.stop>
            <div class="search-wrapper" :class="{ 'search-wrapper-active': showHistory }">
              <input
                v-model="searchQuery"
                type="text"
            class="search-input"
                placeholder="搜索商品名称/品牌/型号"
                @focus="showHistory = true"
                @keydown="handleKeyDown"
          >
              <button class="search-button" @click="handleSearch">
              <SearchOutlined />
              </button>
            </div>
            <!-- 搜索历史 -->
            <div v-show="showHistory && searchHistory.length > 0" class="search-history">
              <div class="history-header">
                <h3>历史搜索</h3>
                <button @click.stop="clearHistory" class="clear-btn">
                  清空
                </button>
              </div>
              <div class="history-list">
                <div
                  v-for="(item, index) in searchHistory"
                  :key="index"
                  class="history-item"
                  @click="useHistoryItem(item)"
                >
                  {{ item }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="nav-right">
          <router-link 
            v-if="userStore.isLoggedIn" 
            to="/profile" 
            class="user-link"
          >
            <a-space>
              <a-avatar :size="32">
                {{ userStore.username?.charAt(0).toUpperCase() }}
              </a-avatar>
              {{ userStore.username }}
            </a-space>
          </router-link>
          <router-link 
            v-else 
            to="/login" 
            class="login-btn"
          >
            <a-button type="primary">登录</a-button>
          </router-link>

          <router-link to="/cart" class="cart-link">
            <a-badge :count="cartStore.totalQuantity" :offset="[8, 0]">
              <ShoppingCartOutlined style="font-size: 24px" />
            </a-badge>
          </router-link>
        </div>
      </div>
    </a-layout-header>

    <a-layout-content class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" :search-keyword="searchQuery" @search="handleSearch"/>
        </transition>
      </router-view>
    </a-layout-content>

    <a-layout-footer class="footer">
      <div class="footer-content">
        <p>&copy; 2024 运动鞋商城. All rights reserved.</p>
      </div>
    </a-layout-footer>
  </a-layout>
</template>

<style>
#app {
  width: 100%;
  height: 100vh;
}
</style>

<style scoped>
.layout {
  min-height: 100vh;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 0;
  height: 80px !important;
  line-height: 80px !important;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  gap: 32px;
}

.nav-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.nav-center {
  flex: 1;
  max-width: 500px;
  display: flex;
  align-items: center;
}

.nav-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 24px;

}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #1677ff;
  text-decoration: none;
  transition: color 0.3s;
  line-height: 1;
}

.logo:hover {
  color: #4096ff;
}

.search-container {
  flex: 1;
  max-width: 600px;
  position: relative;
}

.search-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s;
  height: 32px;
}

.search-wrapper-active {
  border-color: #ff4400;
  box-shadow: 0 2px 8px rgba(255, 68, 0, 0.1);
}

.search-input {
  flex: 1;
  width: 100%;
  height: 100%;
  padding: 0 16px;
  border: none;
  font-size: 14px;
  outline: none;
  background: transparent;
}

.search-button {
  width: 32px;
  height: 100%;
  border: none;
  background: transparent;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  padding: 0;
}

.search-wrapper-active .search-button {
  background: #ff4400;
  color: white;
}

.search-history {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  background: white;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  line-height: normal;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f5f5f5;
}

.history-header h3 {
  margin: 0;
  font-size: 14px;
  color: #333;
  font-weight: normal;
}

.clear-btn {
  background: none;
  border: none;
  color: #999;
  font-size: 12px;
  cursor: pointer;
  padding: 2px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.clear-btn:hover {
  background: #f5f5f5;
  color: #666;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-item {
  padding: 4px 12px;
  background: #f5f5f5;
  border-radius: 16px;
  font-size: 13px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.history-item:hover {
  background: #ff440015;
  color: #ff4400;
}

.user-link {
  color: rgba(0, 0, 0, 0.88);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-link:hover {
  color: #1677ff;
}

.login-btn {
  text-decoration: none;
}

.cart-link {
  color: rgba(0, 0, 0, 0.88);
  text-decoration: none;
  display: flex;
  align-items: center;
  height: 40px;
  transition: color 0.3s;
}

.cart-link:hover {
  color: #1677ff;
}

.main-content {
  padding: 104px 24px 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 80px);
}

.footer {
  text-align: center;
  background: #f5f5f5;
  border-top: 1px solid #f0f0f0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  color: rgba(0, 0, 0, 0.45);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

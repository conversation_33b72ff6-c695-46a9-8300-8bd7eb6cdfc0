<template>
  <div class="cart-page">
    <header class="header">
      <div class="header-content">
        <el-button icon="Back" @click="$router.back()">返回</el-button>
        <h1 class="page-title">购物车</h1>
      </div>
    </header>

    <div class="cart-container">
      <el-empty
        v-if="!cartStore.items.length"
        description="购物车是空的"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/')">
            去购物
          </el-button>
        </template>
      </el-empty>

      <template v-else>
        <div class="cart-list">
          <el-card v-for="item in cartStore.items" :key="item.id" class="cart-item">
            <div class="item-content">
              <el-checkbox
                v-model="item.selected"
                @change="updateSelection"
              />
              <div class="item-image">
                <el-image
                  :src="item.image"
                  fit="cover"
                  :preview-src-list="[item.image]"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="item-info">
                <h3 class="item-name">{{ item.name }}</h3>
                <div class="item-size">尺码：{{ item.size }}</div>
                <div class="item-price">¥{{ item.price }}</div>
              </div>
              <div class="item-quantity">
                <el-input-number
                  v-model="item.quantity"
                  :min="1"
                  :max="10"
                  @change="(val) => updateQuantity(item.id, val)"
                />
              </div>
              <div class="item-total">
                ¥{{ (item.price * item.quantity).toFixed(2) }}
              </div>
              <el-button
                type="danger"
                circle
                @click="removeItem(item.id)"
                class="remove-button"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </el-card>
        </div>

        <div class="cart-footer">
          <div class="select-all">
            <el-checkbox
              v-model="allSelected"
              @change="toggleSelectAll"
            >
              全选
            </el-checkbox>
          </div>
          <div class="cart-summary">
            <div class="total-items">
              已选择 {{ selectedItems.length }} 件商品
            </div>
            <div class="total-price">
              合计：<span class="price">¥{{ totalPrice.toFixed(2) }}</span>
            </div>
            <el-button
              type="primary"
              size="large"
              :disabled="!selectedItems.length"
              @click="checkout"
            >
              结算
            </el-button>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useCartStore } from '../stores/cart'
import { Picture, Delete } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

const cartStore = useCartStore()

// 为每个商品添加选中状态
const items = computed(() => {
  return cartStore.items.map(item => ({
    ...item,
    selected: ref(true)
  }))
})

// 全选状态
const allSelected = computed({
  get: () => items.value.every(item => item.selected),
  set: (value) => {
    items.value.forEach(item => item.selected = value)
  }
})

// 已选择的商品
const selectedItems = computed(() => {
  return items.value.filter(item => item.selected)
})

// 总价
const totalPrice = computed(() => {
  return selectedItems.value.reduce((total, item) => {
    return total + (item.price * item.quantity)
  }, 0)
})

// 更新商品数量
const updateQuantity = (id, quantity) => {
  cartStore.updateQuantity(id, quantity)
}

// 移除商品
const removeItem = async (id) => {
  try {
    await ElMessageBox.confirm(
      '确定要从购物车中移除该商品吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    cartStore.removeFromCart(id)
  } catch {
    // 用户取消删除
  }
}

// 切换全选状态
const toggleSelectAll = (value) => {
  items.value.forEach(item => item.selected = value)
}

// 更新选中状态
const updateSelection = () => {
  // 触发计算属性更新
}

// 结算
const checkout = () => {
  // TODO: 实现结算逻辑
  console.log('结算商品:', selectedItems.value)
}
</script>

<style scoped>
.cart-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 60px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
}

.cart-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
}

.cart-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cart-item {
  background-color: #fff;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px;
}

.item-image {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
}

.el-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.image-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #909399;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  margin: 0 0 8px;
  font-size: 16px;
  line-height: 1.4;
}

.item-size {
  color: #666;
  margin-bottom: 8px;
}

.item-price {
  color: #ff4d4f;
  font-size: 18px;
  font-weight: bold;
}

.item-quantity {
  width: 120px;
}

.item-total {
  width: 100px;
  text-align: right;
  font-size: 18px;
  font-weight: bold;
  color: #ff4d4f;
}

.remove-button {
  flex-shrink: 0;
}

.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-summary {
  display: flex;
  align-items: center;
  gap: 24px;
}

.total-items {
  color: #666;
}

.total-price {
  font-size: 16px;
}

.price {
  color: #ff4d4f;
  font-size: 24px;
  font-weight: bold;
}

@media (max-width: 768px) {
  .item-content {
    flex-wrap: wrap;
  }

  .item-info {
    width: 100%;
    order: -1;
    margin-bottom: 16px;
  }

  .item-quantity,
  .item-total {
    width: auto;
  }
}
</style> 
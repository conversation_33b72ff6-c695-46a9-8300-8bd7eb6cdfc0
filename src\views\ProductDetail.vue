<template>
  <div class="product-detail">
    <header class="header">
      <div class="header-content">
        <el-button icon="Back" @click="$router.back()">返回</el-button>
        <div class="header-right">
          <el-button @click="$router.push('/cart')">
            <el-badge :value="cartStore.totalItems" :hidden="!cartStore.totalItems">
              购物车
            </el-badge>
          </el-button>
        </div>
      </div>
    </header>

    <div class="detail-container" v-loading="loading">
      <el-empty v-if="!loading && !product" description="商品不存在" />

      
      <template v-if="product">
        <div class="product-gallery">

         
          <!-- <el-carousel :interval="4000" type="card" height="400px">
            <el-carousel-item v-for="(p_image, index) in product.productImageList" :key="index">
              
              <el-image
                :src="p_image"
                fit="contain"
                :preview-src-list="product.productImageList"
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image> 
              <el-image :src="p_image" fit="contain">
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <img :src="p_image" style="width: 200px" />
            
              
            </el-carousel-item>
          </el-carousel> -->

         
          <!-- <el-carousel :interval="4000" >
            <el-carousel-item v-for="(p_image, index) in product.productImageList" :key="p_image">
              <img :src="p_image" />
            </el-carousel-item>
          </el-carousel>   -->
         
          <img :src="product.productImageList[0]" style="width: 100%" />
        </div>

        <div class="product-info">
          <h1 class="product-name">{{ product.productName }}</h1>
          
          <div class="price-section">
            <div class="current-price">¥{{ product.discountPrice }}</div>
            <div v-if="product.originalPrice" class="original-price">
              ¥{{ product.originalPrice }}
            </div>
            <div v-if="product.discount" class="discount-tag">
              {{ product.discount }}折
            </div>
          </div>

          <div class="sales-info">
            已售 {{ product.salesVolume }} | 收藏 {{ product.favorites }}
          </div>

          <div class="size-section">
            <div class="section-title">选择尺码</div>
            <div class="size-list">
              <el-radio-group v-model="selectedSize">
                <el-radio-button
                  v-for="size in product.sizes"
                  :key="size.value"
                  :label="size.value"
                  :disabled="size.stock <= 0"
                >
                  {{ size.value }}
                  <template v-if="size.stock <= 5">
                    <div class="stock-warning">仅剩{{ size.stock }}件</div>
                  </template>
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="quantity-section">
            <div class="section-title">数量</div>
            <el-input-number
              v-model="quantity"
              :min="1"
              :max="10"
              :disabled="!selectedSize"
            />
          </div>

          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              :disabled="!selectedSize"
              @click="addToCart"
            >
              加入购物车
            </el-button>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart'
import { Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { getProductDetail } from '../api/product'

const route = useRoute()
const router = useRouter()
const cartStore = useCartStore()

const product = ref(null)
const loading = ref(true)
const selectedSize = ref('')
const quantity = ref(1)

// 加载商品详情
const loadProduct = async () => {
  loading.value = true
  try {
    // TODO: 替换为实际的API调用
    console.log("加载商品详情: "+ route.params.id)
    
    // const response = await axios.get(`/product/detail/${route.params.id}`)
    const response = await getProductDetail(route.params.id)
    product.value = response.data
    console.log("商品详情: "+ product)
    console.log("商品图片地址详情: "+ product.value.productImageList)
  } catch (error) {
    console.error('加载商品详情失败:', error)
    ElMessage.error('加载商品详情失败')
  } finally {
    loading.value = false
  }
}

// 添加到购物车
const addToCart = () => {
  if (!selectedSize.value) {
    ElMessage.warning('请选择尺码')
    return
  }

  const selectedSizeInfo = product.value.sizes.find(
    size => size.value === selectedSize.value
  )

  if (selectedSizeInfo.stock < quantity.value) {
    ElMessage.warning('库存不足')
    return
  }

  cartStore.addToCart(
    {
      id: product.value.id,
      name: product.value.name,
      price: product.value.price,
      image: product.value.images[0],
      size: selectedSize.value
    },
    quantity.value
  )

  ElMessage.success('已添加到购物车')
}

onMounted(() => {
  loadProduct()
})
</script>

<style scoped>


.product-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 60px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.product-gallery {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
}

.el-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #909399;
}

.product-info {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
}

.product-name {
  margin: 0 0 20px;
  font-size: 24px;
  line-height: 1.4;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 16px;
}

.current-price {
  font-size: 32px;
  color: #ff4d4f;
  font-weight: bold;
}

.original-price {
  font-size: 16px;
  color: #999;
  text-decoration: line-through;
}

.discount-tag {
  background-color: #ff4d4f;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.sales-info {
  color: #666;
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
}

.size-section {
  margin-bottom: 24px;
}

.size-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.stock-warning {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
}

.quantity-section {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

@media (max-width: 768px) {
  .detail-container {
    grid-template-columns: 1fr;
  }
}
</style> 